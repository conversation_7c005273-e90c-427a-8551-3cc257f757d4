package cz.pbktechnology.platform.common.minio

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.instrument.InstrumenterFactory.Companion.MINIO_INSTRUMENTER
import cz.pbktechnology.platform.common.context.instrument.MinioRequest
import cz.pbktechnology.platform.common.observability.extensions.executeWithSpan
import io.micronaut.aop.InterceptorBean
import io.micronaut.aop.MethodInterceptor
import io.micronaut.aop.MethodInvocationContext
import io.micronaut.core.annotation.Nullable
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import jakarta.inject.Named
import jakarta.inject.Singleton

/**
 * AOP interceptor that automatically instruments MinIO operations with OpenTelemetry spans.
 */
@Singleton
@InterceptorBean(InstrumentMinio::class)
class MinioInstrumentationInterceptor(
    @Nullable
    @Named(MINIO_INSTRUMENTER)
    private val minioInstrumenter: Instrumenter<MinioRequest, Any>?,
) : MethodInterceptor<Any, Any> {
    override fun intercept(context: MethodInvocationContext<Any, Any>): Any? {
        if (minioInstrumenter == null) {
            return context.proceed()
        }

        val methodName = context.methodName
        val args = context.parameterValues
        val (bucketName, objectName) = extractBucketAndObject(args.firstOrNull())

        val operationContext = ContextConfiguration.operationContext.get()
        val correlationId = operationContext?.xCorrelationId?.toString() ?: "unknown"

        val request =
            MinioRequest(
                correlationId = correlationId,
                bucketName = bucketName,
                objectName = objectName,
                operationName = methodName,
            )

        return minioInstrumenter.executeWithSpan(request) {
            context.proceed()
        }
    }

    /**
     * Helper method to extract bucket and object names from method arguments.
     * This handles the common MinIO args pattern where the first parameter contains bucket/object info.
     */
    private fun extractBucketAndObject(firstArg: Any?): Pair<String, String> {
        if (firstArg == null) return "" to ""

        return try {
            val bucketName =
                try {
                    firstArg.javaClass.getMethod("bucket").invoke(firstArg) as? String ?: ""
                } catch (_: Exception) {
                    ""
                }

            val objectName =
                try {
                    firstArg.javaClass.getMethod("object").invoke(firstArg) as? String ?: ""
                } catch (_: Exception) {
                    ""
                }

            bucketName to objectName
        } catch (_: Exception) {
            "" to ""
        }
    }
}
