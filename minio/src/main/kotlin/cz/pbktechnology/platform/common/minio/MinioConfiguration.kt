package cz.pbktechnology.platform.common.minio

import io.micronaut.context.annotation.ConfigurationProperties
import io.minio.http.HttpUtils
import jakarta.inject.Singleton
import okhttp3.OkHttpClient
import okhttp3.Protocol
import java.io.IOException
import java.security.GeneralSecurityException
import java.util.concurrent.TimeUnit

@Singleton
class MinioConfiguration(
    val properties: MinioConfigurationProperties,
) {
    fun newHttpClient(
        connectTimeout: Long,
        writeTimeout: Long,
        readTimeout: Long,
    ): OkHttpClient {
        var httpClient =
            OkHttpClient()
                .newBuilder()
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(writeTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .protocols(listOf(Protocol.HTTP_1_1))
                .addInterceptor(ConnectionClosingRequestInterceptor())
                .addInterceptor(
                    CustomLoggingInterceptor().apply {
                        level = CustomLoggingInterceptor.Level.HEADERS
                    },
                ).build()

        val filename = System.getenv("SSL_CERT_FILE")
        if (filename != null && !filename.isEmpty()) {
            httpClient =
                try {
                    HttpUtils.enableExternalCertificates(httpClient, filename)
                } catch (e: GeneralSecurityException) {
                    throw RuntimeException(e)
                } catch (e: IOException) {
                    throw RuntimeException(e)
                }
        }
        return httpClient
    }

    fun createMinioClient(properties: MinioConfigurationProperties): io.minio.MinioClient =
        io.minio.MinioClient
            .builder()
            .endpoint(properties.url)
            .httpClient(
                newHttpClient(
                    connectTimeout = properties.connectionTimeout,
                    readTimeout = properties.readTimeout,
                    writeTimeout = properties.writeTimeout,
                ),
            ).also { builder ->
                if (properties.region.isNotEmpty()) {
                    builder.region(properties.region)
                }
            }.credentials(properties.accessKey, properties.secretKey)
            .build()
}

@ConfigurationProperties("minio")
class MinioConfigurationProperties {
    var url: String = ""
    var accessKey: String = ""
    var secretKey: String = ""
    var region: String = ""
    var readTimeout: Long = TimeUnit.MINUTES.toMillis(5)
    var writeTimeout: Long = TimeUnit.MINUTES.toMillis(5)
    var connectionTimeout: Long = TimeUnit.MINUTES.toMillis(5)
    var partSize: Long =
        10_485_760 // 10 MB. Value taken from minio docs: https://min.io/docs/minio/linux/developers/java/API.html#putObject
}
