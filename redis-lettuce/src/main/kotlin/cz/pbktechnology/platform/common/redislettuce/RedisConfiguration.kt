package cz.pbktechnology.platform.common.redislettuce

import io.micronaut.context.annotation.ConfigurationProperties
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@ConfigurationProperties("redis")
class RedisConfiguration {
    var initConnectionRetryDelay = 5.seconds.toJavaDuration()
    var defaultServerEnabled = true
    var defaultServerHealthIndicatorEnabled = true
    var telemetryEnabled = false
}
