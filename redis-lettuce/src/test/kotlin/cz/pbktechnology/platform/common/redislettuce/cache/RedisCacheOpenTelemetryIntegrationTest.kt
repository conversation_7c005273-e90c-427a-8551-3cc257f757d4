package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.OperationContext
import cz.pbktechnology.platform.common.redislettuce.REDIS_IMAGE_NAME
import cz.pbktechnology.platform.common.redislettuce.REDIS_PORT
import io.lettuce.core.api.StatefulRedisConnection
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.CacheInvalidate
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Replaces
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.micronaut.test.support.TestPropertyProvider
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.api.common.AttributeKey
import io.opentelemetry.api.trace.SpanKind
import io.opentelemetry.sdk.testing.junit5.OpenTelemetryExtension
import jakarta.inject.Inject
import jakarta.inject.Singleton
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.RegisterExtension
import org.testcontainers.containers.GenericContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.util.UUID

@Testcontainers
@MicronautTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RedisCacheOpenTelemetryIntegrationTest : TestPropertyProvider {
    companion object {
        @JvmField
        @RegisterExtension
        val otelTesting: OpenTelemetryExtension = OpenTelemetryExtension.create()

        @Container
        private val redisTestContainer = GenericContainer(REDIS_IMAGE_NAME).withExposedPorts(REDIS_PORT)
    }

    override fun getProperties() =
        mapOf(
            "redis.host" to redisTestContainer.host,
            "redis.port" to redisTestContainer.getMappedPort(REDIS_PORT).toString(),
            "redis.caches.user-cache" to "",
            "redis.caches.product-cache" to "",
            "redis.telemetryEnabled" to "true",
            "otel.traces.exporter" to "none",
            "otel.metrics.exporter" to "none",
            "otel.logs.exporter" to "none",
        )

    @Inject
    private lateinit var userService: UserService

    @Inject
    private lateinit var productService: ProductService

    @Inject
    private lateinit var statefulRedisConnection: StatefulRedisConnection<String, String>

    @BeforeEach
    fun clearRedis() {
        statefulRedisConnection.sync().flushdb()
        userService.callCount = 0
        productService.callCount = 0
        otelTesting.clearSpans()
    }

    @Test
    fun `Redis cache operations should create spans with correct attributes`() {
        val correlationId = UUID.randomUUID()

        ContextConfiguration.operationContext.set(
            OperationContext(correlationId),
        )

        otelTesting.clearSpans()

        userService.getUser("test-user") // miss
        userService.getUser("test-user") // hit
        userService.evictUser("test-user") // invalidate
        userService.getUser("test-user") // miss again

        val spans = otelTesting.spans
        val redisSpans = spans.filter { it.name.startsWith("redis:op=") }

        assertThat(redisSpans)
            .describedAs("Should have 6 spans: GET+PUT, GET, INVALIDATE, GET+PUT")
            .hasSize(6)

        val getSpans = redisSpans.filter { it.name.contains("op=GET") }
        val putSpans = redisSpans.filter { it.name.contains("op=PUT") }
        val invalidateSpans = redisSpans.filter { it.name.contains("op=INVALIDATE") && !it.name.contains("op=INVALIDATE_ALL") }

        assertThat(getSpans).hasSize(3)
        assertThat(putSpans).hasSize(2)
        assertThat(invalidateSpans).hasSize(1)

        redisSpans.forEach { span ->
            assertThat(span.attributes.get(AttributeKey.stringKey("cache.name"))).isEqualTo("user-cache")
            assertThat(span.attributes.get(AttributeKey.stringKey("cache.key"))).isEqualTo("test-user")
            assertThat(span.attributes.get(AttributeKey.stringKey("X-Correlation-Id"))).isEqualTo(correlationId.toString())
            assertThat(span.kind).isEqualTo(SpanKind.CLIENT)
        }
    }

    @Test
    fun `Cache operations without context should still work`() {
        val user = userService.getUser("no-context-user")
        assertThat(user).isEqualTo("User: no-context-user")
    }

    @Test
    fun `Different cache names should create spans with correct cache names`() {
        val correlationId = UUID.randomUUID()

        ContextConfiguration.operationContext.set(
            OperationContext(correlationId),
        )

        otelTesting.clearSpans()

        userService.getUser("multi-cache-user")
        userService.getUser("multi-cache-user") // cache hit

        productService.getProduct("multi-cache-product")
        productService.getProduct("multi-cache-product") // cache hit

        val spans = otelTesting.spans
        val redisSpans = spans.filter { it.name.startsWith("redis:op=") }

        assertThat(redisSpans).hasSize(6)

        val userCacheSpans =
            redisSpans.filter {
                it.attributes.get(AttributeKey.stringKey("cache.name")) == "user-cache"
            }
        assertThat(userCacheSpans)
            .describedAs("user-cache spans (GET miss + PUT + GET hit = 3 spans)")
            .hasSize(3)

        val productCacheSpans =
            redisSpans.filter {
                it.attributes.get(AttributeKey.stringKey("cache.name")) == "product-cache"
            }
        assertThat(productCacheSpans)
            .describedAs("product-cache spans (GET miss + PUT + GET hit = 3 spans)")
            .hasSize(3)
    }

    @Singleton
    @CacheConfig("user-cache")
    open class UserService {
        var callCount = 0

        @Cacheable
        open fun getUser(userId: String): String {
            callCount++
            return "User: $userId"
        }

        @CacheInvalidate
        open fun evictUser(userId: String) {
        }
    }

    @Singleton
    @CacheConfig("product-cache")
    open class ProductService {
        var callCount = 0

        @Cacheable
        open fun getProduct(productId: String): String {
            callCount++
            return "Product: $productId"
        }
    }

    @Factory
    class TestOpenTelemetryFactory {
        @Singleton
        @Replaces(OpenTelemetry::class)
        fun testOpenTelemetry(): OpenTelemetry = otelTesting.openTelemetry
    }
}
