package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.instrument.InstrumenterFactory.Companion.REDIS_INSTRUMENTER
import cz.pbktechnology.platform.common.context.instrument.RedisOperation
import cz.pbktechnology.platform.common.context.instrument.RedisRequest
import cz.pbktechnology.platform.common.observability.extensions.executeWithSpan
import io.lettuce.core.api.StatefulConnection
import io.micronaut.cache.SyncCache
import io.micronaut.context.annotation.Requires
import io.micronaut.core.annotation.Nullable
import io.micronaut.core.type.Argument
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.Optional
import java.util.function.Supplier

@Singleton
@Requires(beans = [OpenTelemetry::class])
@Requires(property = "redis.telemetryEnabled", value = "true", defaultValue = "false")
class InstrumentedRedisCache(
    @Nullable
    @Named(REDIS_INSTRUMENTER)
    private val redisInstrumenter: Instrumenter<RedisRequest, Any>?,
) {
    fun wrap(delegate: SyncCache<StatefulConnection<ByteArray, ByteArray>>): SyncCache<StatefulConnection<ByteArray, ByteArray>> =
        if (redisInstrumenter != null) {
            InstrumentedRedisCacheWrapper(delegate, redisInstrumenter)
        } else {
            delegate
        }

    private class InstrumentedRedisCacheWrapper(
        private val delegate: SyncCache<StatefulConnection<ByteArray, ByteArray>>,
        private val redisInstrumenter: Instrumenter<RedisRequest, Any>,
    ) : SyncCache<StatefulConnection<ByteArray, ByteArray>> by delegate {
        override fun <T : Any?> get(
            key: Any,
            requiredType: Argument<T>,
        ): Optional<T> =
            withSpan(key, RedisOperation.GET) {
                delegate.get(key, requiredType)
            }

        override fun <T : Any?> get(
            key: Any,
            requiredType: Argument<T>,
            supplier: Supplier<T>,
        ): T =
            withSpan(key, RedisOperation.GET) {
                delegate.get(key, requiredType, supplier)
            }

        override fun <T : Any?> putIfAbsent(
            key: Any,
            value: T,
        ): Optional<T> =
            withSpan(key, RedisOperation.PUT_IF_ABSENT) {
                delegate.putIfAbsent(key, value)
            }

        override fun put(
            key: Any,
            value: Any,
        ) {
            withSpan(key, RedisOperation.PUT) {
                delegate.put(key, value)
            }
        }

        override fun invalidate(key: Any) {
            withSpan(key, RedisOperation.INVALIDATE) {
                delegate.invalidate(key)
            }
        }

        override fun invalidateAll() {
            withSpan("*", RedisOperation.INVALIDATE_ALL) {
                delegate.invalidateAll()
            }
        }

        private fun <T> withSpan(
            key: Any,
            operation: RedisOperation,
            action: () -> T,
        ): T {
            val operationContext = ContextConfiguration.operationContext.get()
            val correlationId = operationContext?.xCorrelationId?.toString() ?: "unknown"

            val request =
                RedisRequest(
                    correlationId = correlationId,
                    cacheKey = key.toString(),
                    cacheName = name,
                    operation = operation,
                )

            return redisInstrumenter.executeWithSpan(request) { _ -> action() }
        }
    }
}
