package cz.pbktechnology.platform.common.minio

import io.micronaut.aop.Around

/**
 * Annotation to mark classes or methods for MinIO operation instrumentation.
 * When applied to a class, all public methods will be instrumented.
 * When applied to a method, only that specific method will be instrumented.
 */
@Target(AnnotationTarget.CLASS, AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
@Around
annotation class InstrumentMinio
