package cz.pbktechnology.platform.common.minio

import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.minio.MinioClient
import io.opentelemetry.api.OpenTelemetry
import jakarta.inject.Singleton

/**
 * OpenTelemetry-instrumented MinioClient that replaces [StandardMinioClient] when telemetry is enabled.
 *
 * Activated when:
 * - OpenTelemetry is available (`@Requires(beans = [OpenTelemetry::class])`)
 * - Feature flag is enabled (`minio.telemetryEnabled=true`, defaults to `false`)
 *
 * @see StandardMinioClient The default implementation
 */
@Singleton
@Replaces(StandardMinioClient::class)
@Requires(beans = [OpenTelemetry::class])
@Requires(property = "minio.telemetryEnabled", value = "true", defaultValue = "false")
@InstrumentMinio
open class InstrumentedMinioClient(
    minioConfiguration: MinioConfiguration,
    properties: MinioConfigurationProperties,
) : MinioClient(minioConfiguration.createMinioClient(properties))
