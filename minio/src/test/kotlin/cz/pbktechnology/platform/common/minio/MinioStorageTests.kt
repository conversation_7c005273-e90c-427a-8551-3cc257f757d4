package cz.pbktechnology.platform.common.minio

import arrow.core.computations.ResultEffect.bind
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.context.OperationContext
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Replaces
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.api.common.AttributeKey
import io.opentelemetry.sdk.testing.junit5.OpenTelemetryExtension
import jakarta.inject.Inject
import jakarta.inject.Singleton
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import java.io.ByteArrayInputStream
import java.io.File
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@MicronautTest
class MinioStorageTests : MinioTestContainerBase() {
    @Inject
    lateinit var minioStorage: MinioStorageImpl

    override fun getProperties() =
        super.getProperties() +
            mapOf(
                "otel.traces.exporter" to "none",
                "otel.metrics.exporter" to "none",
                "otel.logs.exporter" to "none",
                "minio.telemetryEnabled" to "true",
            )

    @Test
    fun `upload and verify file`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()

        val saveResult = minioStorage.saveFile(filename, fileBytes, "text/plain")
        assertTrue(saveResult.isRight(), "File upload failed")

        val exists = minioStorage.fileExists(filename)
        assertTrue(exists.isRight())
        assertTrue(exists.bind(), "File should exist but does not")
    }

    @Test
    fun `upload file as stream and verify file`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()

        val saveResult = minioStorage.saveFileStream(filename, ByteArrayInputStream(fileBytes), "text/plain")
        assertTrue(saveResult.isRight(), "File upload failed")

        val exists = minioStorage.fileExists(filename)
        assertTrue(exists.isRight())
        assertTrue(exists.bind(), "File should exist but does not")
    }

    @Test
    fun `download file`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()

        minioStorage.saveFile(filename, fileBytes, "text/plain")

        val file = minioStorage.downloadFile(filename)
        assertTrue(file.isRight(), "File download failed")
        assertEquals(fileBytes.decodeToString(), file.bind().decodeToString())
    }

    @Test
    fun `download file with metadata`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()
        val userMetadata = mapOf("key1" to "value1", "key2" to "value2")
        val version = minioStorage.saveFile(filename, fileBytes, "text/plain", userMetadata).bind()

        val fileWithMetadata = minioStorage.downloadFileWithMetadata(filename)
        assertTrue(fileWithMetadata.isRight(), "Metadata download failed")
        with(fileWithMetadata.bind()) {
            assertEquals(userMetadata, metadata)
            assertEquals(version, getVersionId())
        }
    }

    @Test
    fun `download file as stream with metadata`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()
        val userMetadata = mapOf("key1" to "value1", "key2" to "value2")
        val version = minioStorage.saveFile(filename, fileBytes, "text/plain", userMetadata).bind()

        val fsWithMetadata = minioStorage.downloadFileStreamWithMetadata(filename)
        assertTrue(fsWithMetadata.isRight(), "Stream download failed")
        assertEquals(
            fileBytes.decodeToString(),
            fsWithMetadata
                .bind()
                .stream
                .readAllBytes()
                .decodeToString(),
        )
        with(fsWithMetadata.bind()) {
            assertEquals(userMetadata, metadata)
            assertEquals(version, getVersionId())
        }
    }

    @Test
    fun `delete file version`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()
        val userMetadata = mapOf("key1" to "value1", "key2" to "value2")
        val version = minioStorage.saveFile(filename, fileBytes, "text/plain", userMetadata).bind()

        val deleteResult = minioStorage.deleteVersion(filename, version)
        assertTrue(deleteResult.isRight(), "Version deletion failed")

        val exists = minioStorage.fileExists(filename)
        assertTrue(exists.isRight())
        assertFalse(exists.bind(), "File should not exist after deletion")
    }

    @Test
    fun `delete file`() {
        val fileBytes = loadTestFile()
        val filename = UUID.randomUUID().toString()
        val userMetadata = mapOf("key1" to "value1", "key2" to "value2")
        minioStorage.saveFile(filename, fileBytes, "text/plain", userMetadata).bind()

        val deleteResult = minioStorage.deleteFile(filename)
        assertTrue(deleteResult.isRight(), "Version deletion failed")

        val exists = minioStorage.fileExists(filename)
        assertTrue(exists.isRight())
        assertFalse(exists.bind(), "File should not exist after deletion")
    }

    @Test
    fun `MinioStorage operations should create telemetry spans`() {
        val correlationId = UUID.randomUUID()
        val filename = "telemetry-test-${UUID.randomUUID()}"
        val fileBytes = loadTestFile()

        ContextConfiguration.operationContext.set(
            OperationContext(correlationId),
        )

        otelTesting.clearSpans()

        val saveResult = minioStorage.saveFile(filename, fileBytes, "text/plain", emptyMap())
        assertTrue(saveResult.isRight(), "File upload failed")

        val fileWithMetadata = minioStorage.downloadFileWithMetadata(filename)
        assertTrue(fileWithMetadata.isRight(), "File download failed")

        val exists = minioStorage.fileExists(filename)
        assertTrue(exists.isRight())
        assertTrue(exists.bind(), "File should exist")

        val deleteResult = minioStorage.deleteFile(filename)
        assertTrue(deleteResult.isRight(), "File deletion failed")

        val spans = otelTesting.spans
        val minioSpans = spans.filter { it.name.startsWith("minio.") }

        assertThat(minioSpans.size)
            .describedAs("Should have MinIO telemetry spans when telemetry is enabled")
            .isGreaterThan(0)

        minioSpans.forEach { span ->
            assertThat(span.attributes.get(AttributeKey.stringKey("X-Correlation-Id")))
                .isEqualTo(correlationId.toString())
            assertThat(span.attributes.get(AttributeKey.stringKey("bucket.name")))
                .isEqualTo(TEST_BUCKET)
        }

        val operationTypes =
            minioSpans
                .map { span ->
                    span.attributes.get(AttributeKey.stringKey("minio.operation"))
                }.toSet()

        assertThat(operationTypes)
            .containsAnyOf("putObject", "getObject", "statObject", "removeObject")
    }

    private fun loadTestFile(): ByteArray {
        val resource = javaClass.classLoader.getResource(FILENAME)
        requireNotNull(resource) { "File not found: $FILENAME" }
        return File(resource.file).readBytes()
    }

    @Singleton
    class MinioStorageImpl : MinioStorage() {
        override fun getTargetBucket(): String = TEST_BUCKET
    }

    @Factory
    class TestOpenTelemetryFactory {
        @Singleton
        @Replaces(OpenTelemetry::class)
        fun testOpenTelemetry(): OpenTelemetry = otelTesting.openTelemetry
    }

    companion object {
        private const val FILENAME = "testfile.txt"

        @JvmField
        @RegisterExtension
        val otelTesting: OpenTelemetryExtension = OpenTelemetryExtension.create()
    }
}
