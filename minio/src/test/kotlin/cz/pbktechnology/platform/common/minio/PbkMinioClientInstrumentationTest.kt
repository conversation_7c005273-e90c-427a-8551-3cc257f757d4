package cz.pbktechnology.platform.common.minio

import io.micronaut.context.ApplicationContext
import io.minio.MinioClient
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PbkMinioClientInstrumentationTest : MinioTestContainerBase() {
    @Test
    fun `should use instrumented MinIO client when telemetry is enabled`() {
        val context =
            ApplicationContext
                .builder()
                .properties(
                    getProperties() +
                        mapOf(
                            "minio.telemetryEnabled" to "true",
                            "micronaut.otel.enabled" to "true",
                            "micronaut.otel.exporter.otlp.enabled" to "false",
                        ),
                ).build()

        context.start()

        val minioClient = context.getBean(MinioClient::class.java)
        assertThat(minioClient)
            .describedAs("MinioClient bean should be available")
            .isNotNull

        assertThat(minioClient)
            .describedAs("MinioClient should be InstrumentedMinioClient when telemetry is enabled")
            .isInstanceOf(InstrumentedMinioClient::class.java)

        context.close()
    }

    @Test
    fun `should use standard MinIO client when telemetry is disabled`() {
        val context =
            ApplicationContext
                .builder()
                .properties(
                    getProperties() +
                        mapOf(
                            "minio.telemetryEnabled" to "false",
                            "micronaut.otel.enabled" to "true",
                            "micronaut.otel.exporter.otlp.enabled" to "false",
                        ),
                ).build()

        context.start()

        val minioClient = context.getBean(MinioClient::class.java)
        assertThat(minioClient)
            .describedAs("MinioClient bean should be available")
            .isNotNull

        assertThat(minioClient)
            .describedAs("MinioClient should be StandardMinioClient when telemetry is disabled")
            .isInstanceOf(StandardMinioClient::class.java)

        context.close()
    }

    @Test
    fun `should use instrumented MinIO client when both telemetry and OpenTelemetry are enabled`() {
        val context =
            ApplicationContext
                .builder()
                .properties(
                    getProperties() +
                        mapOf(
                            "minio.telemetryEnabled" to "true",
                            "micronaut.otel.enabled" to "true",
                            "micronaut.otel.exporter.otlp.enabled" to "false",
                        ),
                ).build()

        context.start()

        val minioClient = context.getBean(MinioClient::class.java)
        assertThat(minioClient)
            .describedAs("MinioClient bean should be available")
            .isNotNull

        assertThat(minioClient)
            .describedAs("MinioClient should be InstrumentedMinioClient when both telemetry and OpenTelemetry are enabled")
            .isInstanceOf(InstrumentedMinioClient::class.java)

        context.close()
    }
}
