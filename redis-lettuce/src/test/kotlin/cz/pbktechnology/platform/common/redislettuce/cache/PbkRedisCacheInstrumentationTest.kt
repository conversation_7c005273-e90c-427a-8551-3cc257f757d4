package cz.pbktechnology.platform.common.redislettuce.cache

import cz.pbktechnology.platform.common.redislettuce.REDIS_IMAGE_NAME
import cz.pbktechnology.platform.common.redislettuce.REDIS_PORT
import io.micronaut.cache.CacheManager
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.ApplicationContext
import io.micronaut.test.support.TestPropertyProvider
import jakarta.inject.Singleton
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.testcontainers.containers.GenericContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers

@Testcontainers
class PbkRedisCacheInstrumentationTest : TestPropertyProvider {
    @Container
    private val redisContainer = GenericContainer(REDIS_IMAGE_NAME).withExposedPorts(REDIS_PORT)

    override fun getProperties() =
        mapOf(
            "redis.host" to redisContainer.host,
            "redis.port" to redisContainer.getMappedPort(REDIS_PORT).toString(),
            "redis.caches.test-cache" to "",
        )

    @Test
    fun `should use instrumented cache when telemetry is enabled`() {
        val context =
            ApplicationContext
                .builder()
                .properties(
                    getProperties() +
                        mapOf(
                            "redis.telemetryEnabled" to "true",
                            "micronaut.otel.enabled" to "true",
                            "micronaut.otel.exporter.otlp.enabled" to "false",
                        ),
                ).build()

        context.start()

        val instrumentedCache = context.findBean(InstrumentedRedisCache::class.java)
        assertThat(instrumentedCache.isPresent)
            .describedAs("InstrumentedRedisCache bean should be created when telemetry is enabled")
            .isTrue

        val cacheManager = context.getBean(CacheManager::class.java)
        val cache = cacheManager.getCache("test-cache")
        assertThat(cache)
            .describedAs("Cache should be available through CacheManager")
            .isNotNull

        val testService = context.getBean(TestCacheService::class.java)
        val result1 = testService.getCachedValue("key1")
        val result2 = testService.getCachedValue("key1")

        assertThat(result1)
            .describedAs("Cache should return the same value for the same key")
            .isEqualTo(result2)

        assertThat(testService.callCount)
            .describedAs("Method should be called only once due to caching")
            .isEqualTo(1)

        context.close()
    }

    @Test
    fun `should not use instrumented cache when telemetry is disabled`() {
        val context =
            ApplicationContext
                .builder()
                .properties(
                    getProperties() +
                        mapOf(
                            "redis.telemetryEnabled" to "false",
                            "micronaut.otel.enabled" to "true",
                            "micronaut.otel.exporter.otlp.enabled" to "false",
                        ),
                ).build()

        context.start()

        val instrumentedCache = context.findBean(InstrumentedRedisCache::class.java)
        assertThat(instrumentedCache.isPresent)
            .describedAs("InstrumentedRedisCache bean should NOT be created when telemetry is disabled")
            .isFalse

        val cacheManager = context.getBean(CacheManager::class.java)
        val cache = cacheManager.getCache("test-cache")
        assertThat(cache)
            .describedAs("Cache should still be available through CacheManager without instrumentation")
            .isNotNull

        val testService = context.getBean(TestCacheService::class.java)
        val result1 = testService.getCachedValue("key2")
        val result2 = testService.getCachedValue("key2")

        assertThat(result1)
            .describedAs("Cache should return the same value for the same key")
            .isEqualTo(result2)

        assertThat(testService.callCount)
            .describedAs("Method should be called only once due to caching")
            .isEqualTo(1)

        context.close()
    }

    @Singleton
    open class TestCacheService {
        var callCount = 0

        @Cacheable("test-cache")
        open fun getCachedValue(key: String): String {
            callCount++
            return "value-$key"
        }
    }
}
