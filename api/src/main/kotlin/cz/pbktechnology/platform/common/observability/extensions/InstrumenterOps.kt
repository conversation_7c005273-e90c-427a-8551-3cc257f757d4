package cz.pbktechnology.platform.common.observability.extensions

import io.opentelemetry.context.Context
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter

/**
 * Executes [action] within an OpenTelemetry instrumentation span, handling the complete
 * span lifecycle including context propagation, scope management, and exception tracking.
 *
 * This extension simplifies the boilerplate required to properly instrument code with OpenTelemetry
 * by automatically:
 * - Starting a new span with the current context as parent
 * - Making the span context current for the duration of the action
 * - Properly ending the span with the result or exception
 * - Ensuring the scope is closed to prevent context leaks
 *
 * @param REQUEST The type of request object containing span attributes
 * @param RESPONSE The type of response returned by the action
 * @param request The request object used to extract span attributes
 * @param action The code block to execute within the span, receives the OpenTelemetry context
 * @return The result of the action
 * @throws Exception Any exception thrown by the action (properly recorded in the span)
 *
 * Example usage:
 * ```kotlin
 * // Wrap any operation with a span
 * val result = instrumenter.executeWithSpan(request) { context ->
 *     cacheClient.get(key)
 * }
 * ```
 */
fun <REQUEST : Any, RESPONSE> Instrumenter<REQUEST, Any>.executeWithSpan(
    request: REQUEST,
    action: (Context) -> RESPONSE,
): RESPONSE {
    val otelContext = start(Context.current(), request)
    val scope = otelContext.makeCurrent()
    return try {
        val result = action(otelContext)
        end(otelContext, request, result, null)
        result
    } catch (e: Exception) {
        end(otelContext, request, null, e)
        throw e
    } finally {
        scope.close()
    }
}
